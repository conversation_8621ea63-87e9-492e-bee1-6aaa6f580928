# DDD重写项目文档目录索引（单体架构版）

## 📋 文档结构总览

```
backend/docs/ddd-rewrite/
├── README.md                           # 项目总览和文档导航
├── 00-文档目录索引.md                   # 本文档，详细的文档索引
├── 01-需求分析文档.md                   # 业务需求和功能分析
├── 02-单体DDD系统设计文档.md            # DDD领域建模设计（单体架构）
├── 03-技术架构文档.md                   # 单体技术架构和实现方案
├── 04-实施计划文档.md                   # 分阶段实施计划
├── 05-开发规范文档.md                   # 开发标准和规范
├── 06-业务流程设计文档.md               # 完整业务流程设计
├── 07-数据库设计方案.md                 # 数据库设计和迁移方案
├── 08-智能推荐系统设计文档.md           # 推荐系统设计（简化版）
├── 09-用户信用体系设计文档.md           # 信用评分和会员管理
├── 10-用户行为分析设计文档.md           # 行为分析和用户画像（简化版）
├── 11-技术栈选择对比文档.md             # 单体架构技术栈选择理由
├── 12-库存管理功能详细设计文档.md       # 基于信任关系的智能库存管理
└── before-rewrite-DDD.sql              # 重写前的数据库结构
```

## 📖 文档详细索引

### 🏗️ DDD设计文档

#### 2. 02-单体DDD系统设计文档.md
- **文档类型**：系统架构设计
- **主要内容**：
  - DDD领域建模设计（适合单体架构）
  - 聚合根、实体、值对象设计
  - 领域服务和应用服务
  - 模块间依赖关系
  - 单体内的清晰边界划分
- **适用人群**：架构师、技术负责人、开发团队
- **更新频率**：架构设计变更时

#### 3. 03-技术架构文档.md
- **文档类型**：技术架构方案
- **核心技术栈**：
  - **Spring Boot 3.5.4** + **Kotlin 2.1.x** (协程支持)
  - **Spring Data JPA + Hibernate 6.6.x** (现代ORM，完美DDD支持)
  - **PostgreSQL 16+** (JSONB支持) + **Redis 7.x** (缓存)
  - **Spring Events** (内部事件驱动) + **简化搜索方案**
- **主要内容**：
  - 单体架构下的DDD实现方案
  - 基于Spring Events的事件驱动设计
  - PostgreSQL为主的数据存储方案
  - 简化的缓存和搜索策略
  - Kotlin现代特性在单体架构中的应用
  - 传统部署和监控方案
- **适用人群**：架构师、技术负责人、运维团队
- **更新频率**：技术方案变更时

### 🎯 专项功能设计

#### 8. 08-智能推荐系统设计文档.md
- **文档类型**：专项功能设计（简化版）
- **主要内容**：
  - 基于数据库的推荐算法实现
  - 简化的推荐服务架构
  - 基于PostgreSQL的推荐数据存储
  - 推荐效果评估（简化版）
- **适用人群**：后端开发、产品经理
- **更新频率**：推荐算法优化时

#### 10. 10-用户行为分析设计文档.md
- **文档类型**：专项功能设计（简化版）
- **主要内容**：
  - 基于数据库的行为数据存储
  - 简化的用户画像构建
  - 基于SQL的行为分析
  - 定时任务的数据处理
- **适用人群**：后端开发、产品经理
- **更新频率**：行为分析模型优化时

#### 11. 11-技术栈选择对比文档.md
- **文档类型**：技术决策分析
- **主要内容**：
  - 单体架构下的技术栈选择
  - Kotlin vs Java 21在单体项目中的对比
  - PostgreSQL vs MySQL单体项目对比
  - 简化技术栈的长期价值评估
  - 单体到微服务的演进路径
- **适用人群**：架构师、技术负责人、开发团队
- **更新频率**：技术栈重大变更时

## 🔗 文档间关系（简化版）

### 依赖关系
```
01-需求分析 → 02-单体DDD设计 → 03-技术架构 → 04-实施计划 → 05-开发规范
     ↓              ↓              ↓
业务流程分析 → 专项功能设计 → 具体实现
```

## 📊 文档统计（精简后）

| 文档类型 | 数量 | 总页数估算 | 维护频率 |
|----------|------|------------|----------|
| 基础设计文档 | 7个 | ~100页 | 中等 |
| 专项功能设计 | 3个 | ~40页 | 较高 |
| **总计** | **10个** | **~140页** | **-** |

## ✅ 文档质量检查清单（单体版）

### 📋 一致性检查项目

#### **基础信息一致性**
- [x] 项目名称：采购系统DDD重写（单体架构） - 统一
- [x] 架构设计：DDD领域建模，单体部署 - 一致
- [x] 演进规划：3阶段18个月（精简版） - 统一

#### **技术栈一致性**
- [x] 核心技术：Spring Boot 3.5.4 + Kotlin 2.1.x + PostgreSQL 16+
- [x] 事件机制：Spring Events（内部事件）
- [x] 缓存方案：Redis 7.x
- [x] 部署方案：Docker + 传统部署

### 📈 当前质量评估：适合单体项目 ✅

这套精简的文档体系专门针对单体架构设计，避免了过度设计，为DDD重写项目提供了实用的设计指导。
