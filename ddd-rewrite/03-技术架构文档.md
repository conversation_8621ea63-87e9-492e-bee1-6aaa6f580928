# 采购系统DDD重写 - 单体技术架构方案

## 1. 技术架构概述

### 1.1 架构原则
- **单体优先**：避免分布式系统复杂性，专注业务价值
- **模块化设计**：清晰的模块边界，为未来演进做准备
- **技术现代化**：采用Kotlin + Spring Boot 3.x现代技术栈
- **运维简化**：减少组件数量，降低运维复杂度

### 1.2 核心技术栈

#### 应用框架层
- **Spring Boot 3.5.4**：现代企业级应用框架，完美支持Kotlin
- **Spring Framework 6.2.x**：核心IoC和AOP框架，原生支持Kotlin特性
- **Spring Security 6.5.0**：企业级安全框架，OAuth2和JWT支持
- **Spring Data JPA + Hibernate 6.6.x**：ORM框架，与Kotlin数据类完美集成
- **Kotlin 2.1.x**：现代编程语言，简洁语法、空安全、协程支持
- **Kotlin Coroutines**：异步编程框架，优雅的并发处理

#### 数据存储层
- **PostgreSQL 16+**：主数据库，强大的JSONB支持和全文搜索能力
- **Redis 7.x**：缓存、会话存储和分布式锁
- **阿里云OSS**：对象存储，文件和图片存储

#### 事件和消息系统（简化版）
- **Spring Events**：Spring内置事件机制，同步/异步事件处理
- **@EventListener + @Async**：简单的异步事件处理
- **@TransactionalEventListener**：事务事件，确保数据一致性

#### 监控和可观测性（简化版）
- **Spring Boot Actuator**：应用健康检查和运行时监控
- **Micrometer 1.15.x**：指标收集和业务监控
- **Logback + 文件日志**：结构化日志，定期轮转
- **Prometheus + Grafana**：监控数据存储和可视化（可选）

#### 部署和运维
- **Docker**：容器化技术
- **Docker Compose**：本地开发环境
- **传统部署**：JAR包 + Systemd服务
- **Nginx**：反向代理和负载均衡

## 2. 架构模式设计

### 2.1 DDD分层架构

```
┌─────────────────────────────────────────┐
│              Web Layer                   │  ← REST API, Web Controllers
├─────────────────────────────────────────┤
│           Application Layer              │  ← Application Services, DTOs
├─────────────────────────────────────────┤
│             Domain Layer                 │  ← Aggregates, Entities, Domain Services
├─────────────────────────────────────────┤
│          Infrastructure Layer            │  ← Repositories, External Services
└─────────────────────────────────────────┘
```

### 2.2 事件驱动架构（简化版）

```kotlin
// 领域事件定义
sealed interface DomainEvent {
    val eventId: String
    val occurredOn: Instant
    val aggregateId: String
}

// 事件发布
@Service
@Transactional
class OrderService(
    private val orderRepository: OrderRepository,
    private val eventPublisher: ApplicationEventPublisher
) {
    
    fun createOrder(command: CreateOrderCommand): OrderId {
        val order = Order.create(command)
        orderRepository.save(order)
        
        // 发布领域事件
        order.domainEvents.forEach { event ->
            eventPublisher.publishEvent(event)
        }
        
        return order.id
    }
}

// 事件监听
@Component
class OrderEventHandler {
    
    @EventListener
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    suspend fun handle(event: OrderCreatedEvent) {
        // 异步处理订单创建后的业务逻辑
        logisticsService.createShipmentRequest(event.orderId)
        notificationService.notifySupplier(event.supplierId)
    }
}
```

### 2.3 数据访问模式

#### Repository模式实现
```kotlin
// 领域仓储接口
interface UserRepository {
    suspend fun findById(id: UserId): User?
    suspend fun findByEmail(email: Email): User?
    suspend fun save(user: User): User
    suspend fun delete(id: UserId)
}

// 基础设施层实现
@Repository
class JpaUserRepository(
    private val jpaRepository: UserJpaRepository
) : UserRepository {
    
    override suspend fun findById(id: UserId): User? = withContext(Dispatchers.IO) {
        jpaRepository.findById(id.value).orElse(null)
    }
    
    override suspend fun save(user: User): User = withContext(Dispatchers.IO) {
        jpaRepository.save(user)
    }
}
```

#### 查询优化策略
```kotlin
// 使用JSONB查询
@Query("""
    SELECT u FROM User u 
    WHERE JSON_EXTRACT(u.profile, '$.company.industry') = :industry
    AND u.status = :status
""")
suspend fun findByIndustryAndStatus(
    industry: String, 
    status: UserStatus
): List<User>

// 使用投影减少数据传输
interface UserSummaryProjection {
    val id: String
    val email: String
    val companyName: String
    val creditScore: Int
}
```

## 3. 数据存储架构

### 3.1 PostgreSQL主数据库设计

#### 核心特性应用
```sql
-- 使用JSONB存储复杂对象
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    profile JSONB NOT NULL,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- JSONB索引优化
CREATE INDEX idx_users_profile_company 
ON users USING GIN ((profile->'company'));

CREATE INDEX idx_users_profile_industry 
ON users ((profile->>'company'->>'industry'));

-- 全文搜索
CREATE INDEX idx_requirements_search 
ON procurement_requirements USING GIN (to_tsvector('english', title || ' ' || description));
```

#### 数据分区策略
```sql
-- 按时间分区存储历史数据
CREATE TABLE order_history (
    id VARCHAR(36),
    order_data JSONB,
    created_at TIMESTAMP
) PARTITION BY RANGE (created_at);

CREATE TABLE order_history_2024 PARTITION OF order_history
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### 3.2 Redis缓存策略

#### 缓存层次设计
```kotlin
@Service
class CachedUserService(
    private val userRepository: UserRepository,
    private val redisTemplate: RedisTemplate<String, String>
) {
    
    @Cacheable(value = ["users"], key = "#id.value")
    suspend fun findById(id: UserId): User? {
        return userRepository.findById(id)
    }
    
    @CacheEvict(value = ["users"], key = "#user.id.value")
    suspend fun save(user: User): User {
        return userRepository.save(user)
    }
    
    // 热点数据预加载
    @Scheduled(fixedRate = 300000) // 5分钟
    suspend fun preloadHotData() {
        val hotUsers = userRepository.findMostActiveUsers(100)
        hotUsers.forEach { user ->
            redisTemplate.opsForValue().set(
                "user:${user.id.value}", 
                objectMapper.writeValueAsString(user),
                Duration.ofHours(1)
            )
        }
    }
}
```

## 4. 性能优化策略

### 4.1 数据库性能优化

#### 连接池配置
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        jdbc:
          batch_size: 25
          order_inserts: true
          order_updates: true
        cache:
          use_second_level_cache: true
          region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
```

#### 查询优化
```kotlin
// 批量查询优化
@Query("""
    SELECT r FROM ProcurementRequirement r 
    JOIN FETCH r.items 
    WHERE r.status = :status 
    AND r.createdAt >= :fromDate
""")
suspend fun findActiveRequirementsWithItems(
    status: RequirementStatus,
    fromDate: LocalDateTime
): List<ProcurementRequirement>

// 分页查询
suspend fun findRequirementsPaged(
    pageable: Pageable,
    filters: RequirementFilters
): Page<RequirementSummary> = withContext(Dispatchers.IO) {
    requirementRepository.findAll(
        RequirementSpecification.withFilters(filters),
        pageable
    ).map { it.toSummary() }
}
```

### 4.2 应用性能优化

#### 协程并发处理
```kotlin
@Service
class RequirementProcessingService {
    
    suspend fun processRequirements(requirements: List<RequirementId>) = coroutineScope {
        requirements.chunked(10).map { chunk ->
            async {
                chunk.map { requirementId ->
                    async {
                        processRequirement(requirementId)
                    }
                }.awaitAll()
            }
        }.awaitAll().flatten()
    }
    
    private suspend fun processRequirement(id: RequirementId): ProcessingResult {
        // 处理单个需求
        return withContext(Dispatchers.IO) {
            // 业务逻辑处理
        }
    }
}
```

## 5. 安全架构

### 5.1 认证和授权
```kotlin
@Configuration
@EnableWebSecurity
class SecurityConfig {
    
    @Bean
    fun securityFilterChain(http: HttpSecurity): SecurityFilterChain {
        return http
            .csrf { it.disable() }
            .sessionManagement { 
                it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) 
            }
            .authorizeHttpRequests { auth ->
                auth
                    .requestMatchers("/api/public/**").permitAll()
                    .requestMatchers("/api/admin/**").hasRole("ADMIN")
                    .requestMatchers("/api/supplier/**").hasRole("SUPPLIER")
                    .requestMatchers("/api/buyer/**").hasRole("BUYER")
                    .anyRequest().authenticated()
            }
            .oauth2ResourceServer { oauth2 ->
                oauth2.jwt { jwt ->
                    jwt.jwtDecoder(jwtDecoder())
                }
            }
            .build()
    }
}
```

### 5.2 数据安全
```kotlin
// 敏感数据加密
@Entity
data class User(
    @Id val id: UserId,
    
    @Convert(converter = EncryptedStringConverter::class)
    val phone: String,
    
    @Convert(converter = EncryptedStringConverter::class)
    val idCardNumber: String
)

// 审计日志
@Entity
@EntityListeners(AuditingEntityListener::class)
data class AuditLog(
    @Id val id: String,
    val userId: String,
    val action: String,
    val resourceType: String,
    val resourceId: String,
    
    @CreatedDate
    val createdAt: Instant
)
```

## 6. 部署架构

### 6.1 容器化部署
```dockerfile
# Dockerfile
FROM openjdk:21-jdk-slim

COPY build/libs/procurement-system-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DATABASE_URL=*************************************
    depends_on:
      - db
      - redis

  db:
    image: postgres:16
    environment:
      POSTGRES_DB: procurement
      POSTGRES_USER: procurement
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

### 6.2 生产部署配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
    
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}
    
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    
logging:
  level:
    com.procurement: INFO
  file:
    name: /var/log/procurement/application.log
    
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
```

## 7. 总结

### 7.1 单体架构优势
- **简单部署**：单一JAR包，降低运维复杂度
- **数据一致性**：数据库事务保证强一致性
- **开发效率**：模块间直接调用，调试简单
- **成本控制**：减少基础设施成本

### 7.2 技术价值
- **现代化技术栈**：Kotlin + Spring Boot 3.x的开发体验
- **DDD架构**：清晰的业务边界和领域模型
- **性能优化**：PostgreSQL + Redis的高性能存储
- **演进能力**：为未来微服务化预留清晰边界

这个技术架构专门为单体应用优化，在保持现代化技术栈的同时，避免了分布式系统的复杂性。
