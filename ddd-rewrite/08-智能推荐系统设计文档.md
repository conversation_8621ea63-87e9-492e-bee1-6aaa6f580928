# 智能需求推荐系统设计 (Kotlin + PostgreSQL + Hibernate + JPA)

## 1. 系统概述

### 1.1 设计目标
构建一个智能化的需求推荐系统，实现买家需求与供应商能力的精准匹配，提升平台的供需对接效率和用户体验。

### 1.2 核心价值
- **供应商价值**：被动接收精准需求推荐，提升商机获取效率
- **买家价值**：获得更多优质供应商响应，提升采购成功率
- **平台价值**：提升用户活跃度和交易转化率

## 2. 推荐系统架构（简化版）

### 2.1 整体架构
```mermaid
graph TB
    subgraph "数据层"
        A[需求数据] --> D[PostgreSQL]
        B[供应商数据] --> D
        C[行为数据] --> D
    end
    
    subgraph "算法层"
        D --> E[基础匹配算法]
        E --> F[协同过滤]
        E --> G[内容推荐]
        F --> H[推荐融合]
        G --> H
    end
    
    subgraph "服务层"
        H --> I[推荐服务]
        I --> J[实时推荐]
        I --> K[批量推荐]
    end
    
    subgraph "应用层"
        J --> L[API推荐]
        K --> M[邮件推荐]
    end
```

### 2.2 核心组件

#### 2.2.1 数据收集组件
- **用户行为追踪**：浏览、搜索、投标行为
- **交易数据分析**：成功交易的特征提取
- **用户画像构建**：基于行为的用户分类

#### 2.2.2 推荐算法组件
- **基于内容的推荐**：产品类别、规格匹配
- **协同过滤推荐**：相似用户行为分析
- **混合推荐策略**：多算法结果融合

#### 2.2.3 推荐服务组件
- **实时推荐引擎**：在线推荐计算
- **批量推荐任务**：离线推荐预计算
- **推荐结果缓存**：Redis缓存优化

## 3. 数据模型设计

### 3.1 用户行为数据模型

#### 3.1.1 用户行为表设计
```sql
-- 用户行为记录表
CREATE TABLE user_behaviors (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    behavior_type VARCHAR(50) NOT NULL CHECK (behavior_type IN ('VIEW', 'SEARCH', 'BID', 'FAVORITE', 'CONTACT')),
    target_type VARCHAR(50) NOT NULL CHECK (target_type IN ('REQUIREMENT', 'SUPPLIER', 'PRODUCT')),
    target_id VARCHAR(36) NOT NULL,
    
    -- 行为详情
    behavior_details JSONB DEFAULT '{}',
    
    -- 行为权重（用于推荐算法）
    weight DECIMAL(3,2) DEFAULT 1.0,
    
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(36),
    ip_address INET,
    user_agent TEXT
);

-- 用户偏好表
CREATE TABLE user_preferences (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    preference_type VARCHAR(50) NOT NULL,
    preference_value VARCHAR(255) NOT NULL,
    preference_score DECIMAL(5,2) DEFAULT 0.0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, preference_type, preference_value)
);

-- 索引设计
CREATE INDEX idx_user_behaviors_user_id ON user_behaviors(user_id);
CREATE INDEX idx_user_behaviors_type_target ON user_behaviors(behavior_type, target_type);
CREATE INDEX idx_user_behaviors_occurred_at ON user_behaviors(occurred_at);
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);
```

#### 3.1.2 Kotlin实体映射
```kotlin
@Entity
@Table(name = "user_behaviors")
data class UserBehavior(
    @Id
    val id: UserBehaviorId,
    
    @Column(name = "user_id")
    val userId: UserId,
    
    @Enumerated(EnumType.STRING)
    @Column(name = "behavior_type")
    val behaviorType: BehaviorType,
    
    @Enumerated(EnumType.STRING)
    @Column(name = "target_type")
    val targetType: TargetType,
    
    @Column(name = "target_id")
    val targetId: String,
    
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "behavior_details", columnDefinition = "jsonb")
    val behaviorDetails: BehaviorDetails,
    
    @Column(name = "weight")
    val weight: BigDecimal = BigDecimal.ONE,
    
    @Column(name = "occurred_at")
    val occurredAt: Instant = Instant.now(),
    
    @Column(name = "session_id")
    val sessionId: String?
)

enum class BehaviorType {
    VIEW, SEARCH, BID, FAVORITE, CONTACT
}

enum class TargetType {
    REQUIREMENT, SUPPLIER, PRODUCT
}
```

### 3.2 推荐结果数据模型

#### 3.2.1 推荐结果表设计
```sql
-- 推荐结果表
CREATE TABLE recommendation_results (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    recommendation_type VARCHAR(50) NOT NULL CHECK (recommendation_type IN ('REQUIREMENT', 'SUPPLIER')),
    target_id VARCHAR(36) NOT NULL,
    
    -- 推荐评分
    score DECIMAL(5,2) NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL,
    
    -- 推荐原因
    reason JSONB DEFAULT '{}',
    
    -- 推荐状态
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'CLICKED', 'IGNORED', 'EXPIRED')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE
);

-- 推荐反馈表
CREATE TABLE recommendation_feedbacks (
    id VARCHAR(36) PRIMARY KEY,
    recommendation_id VARCHAR(36) NOT NULL REFERENCES recommendation_results(id),
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    feedback_type VARCHAR(20) NOT NULL CHECK (feedback_type IN ('LIKE', 'DISLIKE', 'IRRELEVANT', 'HELPFUL')),
    feedback_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引设计
CREATE INDEX idx_recommendation_results_user_id ON recommendation_results(user_id);
CREATE INDEX idx_recommendation_results_type_score ON recommendation_results(recommendation_type, score DESC);
CREATE INDEX idx_recommendation_results_status ON recommendation_results(status);
CREATE INDEX idx_recommendation_feedbacks_recommendation_id ON recommendation_feedbacks(recommendation_id);
```

## 4. 推荐算法实现

### 4.1 基于内容的推荐算法

#### 4.1.1 产品相似度计算
```kotlin
@Service
class ContentBasedRecommendationService(
    private val requirementRepository: ProcurementRequirementRepository,
    private val userBehaviorRepository: UserBehaviorRepository
) {
    
    suspend fun recommendRequirements(userId: UserId, limit: Int = 10): List<RecommendationResult> {
        // 1. 获取用户历史行为
        val userBehaviors = userBehaviorRepository.findByUserIdAndBehaviorType(
            userId, BehaviorType.VIEW, BehaviorType.BID
        )
        
        // 2. 提取用户兴趣特征
        val userInterests = extractUserInterests(userBehaviors)
        
        // 3. 计算需求相似度
        val candidates = requirementRepository.findActiveRequirements()
        val recommendations = candidates.map { requirement ->
            val similarity = calculateContentSimilarity(userInterests, requirement)
            RecommendationResult(
                userId = userId,
                targetId = requirement.id.value,
                score = similarity,
                algorithmType = "CONTENT_BASED",
                reason = buildRecommendationReason(userInterests, requirement)
            )
        }.sortedByDescending { it.score }
        .take(limit)
        
        return recommendations
    }
    
    private fun extractUserInterests(behaviors: List<UserBehavior>): UserInterests {
        val categoryWeights = mutableMapOf<String, Double>()
        val priceRanges = mutableListOf<PriceRange>()
        
        behaviors.forEach { behavior ->
            val details = behavior.behaviorDetails
            // 提取类别偏好
            details.category?.let { category ->
                categoryWeights[category] = categoryWeights.getOrDefault(category, 0.0) + 
                    behavior.weight.toDouble()
            }
            // 提取价格偏好
            details.priceRange?.let { priceRanges.add(it) }
        }
        
        return UserInterests(
            categoryPreferences = categoryWeights,
            pricePreferences = calculatePricePreferences(priceRanges)
        )
    }
    
    private fun calculateContentSimilarity(
        userInterests: UserInterests, 
        requirement: ProcurementRequirement
    ): BigDecimal {
        var similarity = 0.0
        
        // 类别相似度 (权重: 0.4)
        val categoryScore = userInterests.categoryPreferences[requirement.category] ?: 0.0
        similarity += categoryScore * 0.4
        
        // 价格相似度 (权重: 0.3)
        val priceScore = calculatePriceSimilarity(userInterests.pricePreferences, requirement.budgetRange)
        similarity += priceScore * 0.3
        
        // 地理位置相似度 (权重: 0.2)
        val locationScore = calculateLocationSimilarity(userInterests.locationPreferences, requirement.deliveryLocation)
        similarity += locationScore * 0.2
        
        // 时间紧急度相似度 (权重: 0.1)
        val urgencyScore = calculateUrgencySimilarity(userInterests.urgencyPreferences, requirement.urgency)
        similarity += urgencyScore * 0.1
        
        return BigDecimal.valueOf(similarity).setScale(2, RoundingMode.HALF_UP)
    }
}
```

### 4.2 协同过滤推荐算法

#### 4.2.1 用户相似度计算
```kotlin
@Service
class CollaborativeFilteringService(
    private val userBehaviorRepository: UserBehaviorRepository,
    private val recommendationResultRepository: RecommendationResultRepository
) {
    
    suspend fun recommendBasedOnSimilarUsers(userId: UserId, limit: Int = 10): List<RecommendationResult> {
        // 1. 找到相似用户
        val similarUsers = findSimilarUsers(userId, 50)
        
        // 2. 获取相似用户的偏好
        val similarUserBehaviors = userBehaviorRepository.findByUserIds(
            similarUsers.map { it.userId }
        )
        
        // 3. 计算推荐分数
        val targetScores = mutableMapOf<String, Double>()
        
        similarUserBehaviors.groupBy { it.targetId }.forEach { (targetId, behaviors) ->
            val score = behaviors.sumOf { behavior ->
                val userSimilarity = similarUsers.find { it.userId == behavior.userId }?.similarity ?: 0.0
                behavior.weight.toDouble() * userSimilarity
            }
            targetScores[targetId] = score
        }
        
        // 4. 过滤用户已经交互过的项目
        val userInteractedTargets = userBehaviorRepository.findTargetIdsByUserId(userId)
        val filteredScores = targetScores.filterKeys { it !in userInteractedTargets }
        
        // 5. 生成推荐结果
        return filteredScores.entries
            .sortedByDescending { it.value }
            .take(limit)
            .map { (targetId, score) ->
                RecommendationResult(
                    userId = userId,
                    targetId = targetId,
                    score = BigDecimal.valueOf(score).setScale(2, RoundingMode.HALF_UP),
                    algorithmType = "COLLABORATIVE_FILTERING",
                    reason = buildCollaborativeReason(similarUsers, targetId)
                )
            }
    }
    
    private suspend fun findSimilarUsers(userId: UserId, limit: Int): List<UserSimilarity> {
        // 获取目标用户的行为向量
        val targetUserBehaviors = userBehaviorRepository.findByUserId(userId)
        val targetVector = buildUserVector(targetUserBehaviors)
        
        // 获取所有其他用户的行为
        val allUsers = userBehaviorRepository.findAllUserIds().filter { it != userId }
        
        val similarities = allUsers.map { otherUserId ->
            val otherUserBehaviors = userBehaviorRepository.findByUserId(otherUserId)
            val otherVector = buildUserVector(otherUserBehaviors)
            
            val similarity = calculateCosineSimilarity(targetVector, otherVector)
            UserSimilarity(otherUserId, similarity)
        }.filter { it.similarity > 0.1 } // 过滤相似度太低的用户
        .sortedByDescending { it.similarity }
        .take(limit)
        
        return similarities
    }
    
    private fun calculateCosineSimilarity(vector1: Map<String, Double>, vector2: Map<String, Double>): Double {
        val commonKeys = vector1.keys.intersect(vector2.keys)
        if (commonKeys.isEmpty()) return 0.0
        
        val dotProduct = commonKeys.sumOf { key ->
            vector1[key]!! * vector2[key]!!
        }
        
        val norm1 = sqrt(vector1.values.sumOf { it * it })
        val norm2 = sqrt(vector2.values.sumOf { it * it })
        
        return if (norm1 == 0.0 || norm2 == 0.0) 0.0 else dotProduct / (norm1 * norm2)
    }
}
```

### 4.3 混合推荐策略

#### 4.3.1 多算法融合
```kotlin
@Service
class HybridRecommendationService(
    private val contentBasedService: ContentBasedRecommendationService,
    private val collaborativeFilteringService: CollaborativeFilteringService,
    private val popularityBasedService: PopularityBasedRecommendationService
) {
    
    suspend fun generateRecommendations(userId: UserId, limit: Int = 10): List<RecommendationResult> {
        // 1. 并发执行多种推荐算法
        val recommendations = coroutineScope {
            val contentBased = async { 
                contentBasedService.recommendRequirements(userId, limit * 2) 
            }
            val collaborative = async { 
                collaborativeFilteringService.recommendBasedOnSimilarUsers(userId, limit * 2) 
            }
            val popularity = async { 
                popularityBasedService.recommendPopularRequirements(userId, limit) 
            }
            
            awaitAll(contentBased, collaborative, popularity)
        }
        
        // 2. 融合推荐结果
        val fusedRecommendations = fuseRecommendations(
            contentBased = recommendations[0],
            collaborative = recommendations[1],
            popularity = recommendations[2]
        )
        
        // 3. 多样性优化
        val diversifiedRecommendations = diversifyRecommendations(fusedRecommendations)
        
        return diversifiedRecommendations.take(limit)
    }
    
    private fun fuseRecommendations(
        contentBased: List<RecommendationResult>,
        collaborative: List<RecommendationResult>,
        popularity: List<RecommendationResult>
    ): List<RecommendationResult> {
        val targetScores = mutableMapOf<String, FusedScore>()
        
        // 内容推荐权重: 0.4
        contentBased.forEach { rec ->
            targetScores[rec.targetId] = targetScores.getOrDefault(rec.targetId, FusedScore())
                .copy(contentScore = rec.score.toDouble() * 0.4)
        }
        
        // 协同过滤权重: 0.4
        collaborative.forEach { rec ->
            val existing = targetScores.getOrDefault(rec.targetId, FusedScore())
            targetScores[rec.targetId] = existing.copy(
                collaborativeScore = rec.score.toDouble() * 0.4
            )
        }
        
        // 热门推荐权重: 0.2
        popularity.forEach { rec ->
            val existing = targetScores.getOrDefault(rec.targetId, FusedScore())
            targetScores[rec.targetId] = existing.copy(
                popularityScore = rec.score.toDouble() * 0.2
            )
        }
        
        // 计算最终分数并排序
        return targetScores.entries
            .map { (targetId, fusedScore) ->
                val finalScore = fusedScore.contentScore + fusedScore.collaborativeScore + fusedScore.popularityScore
                RecommendationResult(
                    userId = UserId(""), // 临时值
                    targetId = targetId,
                    score = BigDecimal.valueOf(finalScore).setScale(2, RoundingMode.HALF_UP),
                    algorithmType = "HYBRID",
                    reason = buildHybridReason(fusedScore)
                )
            }
            .sortedByDescending { it.score }
    }
    
    private fun diversifyRecommendations(recommendations: List<RecommendationResult>): List<RecommendationResult> {
        val diversified = mutableListOf<RecommendationResult>()
        val usedCategories = mutableSetOf<String>()
        
        // 确保推荐结果的多样性
        recommendations.forEach { rec ->
            // 这里需要获取目标的类别信息
            val category = getTargetCategory(rec.targetId)
            if (usedCategories.size < 3 || category !in usedCategories) {
                diversified.add(rec)
                usedCategories.add(category)
            }
        }
        
        return diversified
    }
}

data class FusedScore(
    val contentScore: Double = 0.0,
    val collaborativeScore: Double = 0.0,
    val popularityScore: Double = 0.0
)
```

## 5. 推荐服务实现

### 5.1 实时推荐服务

#### 5.1.1 推荐API实现
```kotlin
@RestController
@RequestMapping("/api/v1/recommendations")
class RecommendationController(
    private val recommendationService: RecommendationService,
    private val recommendationQueryService: RecommendationQueryService
) {
    
    @GetMapping("/requirements")
    suspend fun getRequirementRecommendations(
        @RequestParam userId: String,
        @RequestParam(defaultValue = "10") limit: Int,
        @RequestParam(defaultValue = "HYBRID") algorithm: String
    ): ResponseEntity<List<RequirementRecommendationResponse>> {
        
        val userIdObj = UserId(userId)
        val recommendations = when (algorithm) {
            "CONTENT" -> recommendationService.getContentBasedRecommendations(userIdObj, limit)
            "COLLABORATIVE" -> recommendationService.getCollaborativeRecommendations(userIdObj, limit)
            "HYBRID" -> recommendationService.getHybridRecommendations(userIdObj, limit)
            else -> recommendationService.getHybridRecommendations(userIdObj, limit)
        }
        
        val responses = recommendations.map { it.toRequirementRecommendationResponse() }
        return ResponseEntity.ok(responses)
    }
    
    @PostMapping("/feedback")
    suspend fun submitFeedback(
        @RequestBody request: RecommendationFeedbackRequest
    ): ResponseEntity<Void> {
        
        val feedback = RecommendationFeedback(
            recommendationId = RecommendationId(request.recommendationId),
            userId = UserId(request.userId),
            feedbackType = request.feedbackType,
            feedbackReason = request.reason
        )
        
        recommendationService.submitFeedback(feedback)
        return ResponseEntity.ok().build()
    }
}

data class RequirementRecommendationResponse(
    val requirementId: String,
    val title: String,
    val category: String,
    val budgetRange: String,
    val location: String,
    val deadline: String,
    val score: BigDecimal,
    val reason: String
)

data class RecommendationFeedbackRequest(
    val recommendationId: String,
    val userId: String,
    val feedbackType: FeedbackType,
    val reason: String?
)
```

### 5.2 批量推荐任务

#### 5.2.1 定时推荐任务
```kotlin
@Component
class BatchRecommendationTask(
    private val recommendationService: RecommendationService,
    private val userRepository: UserRepository,
    private val emailService: EmailService
) {
    
    @Scheduled(cron = "0 0 9 * * ?") // 每天上午9点执行
    suspend fun generateDailyRecommendations() {
        logger.info("Starting daily recommendation generation")
        
        try {
            // 获取活跃用户列表
            val activeUsers = userRepository.findActiveUsers()
            
            // 并发生成推荐
            val batchSize = 100
            activeUsers.chunked(batchSize).forEach { userBatch ->
                coroutineScope {
                    userBatch.map { user ->
                        async {
                            generateUserRecommendations(user.id)
                        }
                    }.awaitAll()
                }
            }
            
            logger.info("Daily recommendation generation completed for ${activeUsers.size} users")
            
        } catch (e: Exception) {
            logger.error("Failed to generate daily recommendations", e)
        }
    }
    
    private suspend fun generateUserRecommendations(userId: UserId) {
        try {
            // 生成推荐
            val recommendations = recommendationService.getHybridRecommendations(userId, 20)
            
            // 保存推荐结果
            recommendationService.saveRecommendations(recommendations)
            
            // 发送推荐邮件（如果用户开启了邮件通知）
            if (shouldSendEmailRecommendation(userId)) {
                emailService.sendRecommendationEmail(userId, recommendations.take(5))
            }
            
        } catch (e: Exception) {
            logger.error("Failed to generate recommendations for user: $userId", e)
        }
    }
    
    companion object {
        private val logger = LoggerFactory.getLogger(BatchRecommendationTask::class.java)
    }
}
```

## 6. 性能优化

### 6.1 缓存策略

#### 6.1.1 Redis缓存实现
```kotlin
@Service
class CachedRecommendationService(
    private val recommendationService: RecommendationService,
    private val redisTemplate: RedisTemplate<String, Any>
) {
    
    suspend fun getRecommendations(userId: UserId, limit: Int): List<RecommendationResult> {
        val cacheKey = "recommendations:${userId.value}:$limit"
        
        // 尝试从缓存获取
        val cached = redisTemplate.opsForValue().get(cacheKey)
        if (cached != null) {
            @Suppress("UNCHECKED_CAST")
            return cached as List<RecommendationResult>
        }
        
        // 缓存未命中，计算推荐
        val recommendations = recommendationService.getHybridRecommendations(userId, limit)
        
        // 存入缓存，过期时间1小时
        redisTemplate.opsForValue().set(cacheKey, recommendations, Duration.ofHours(1))
        
        return recommendations
    }
    
    suspend fun invalidateUserRecommendations(userId: UserId) {
        val pattern = "recommendations:${userId.value}:*"
        val keys = redisTemplate.keys(pattern)
        if (keys.isNotEmpty()) {
            redisTemplate.delete(keys)
        }
    }
}
```

### 6.2 数据库优化

#### 6.2.1 预计算用户特征
```sql
-- 用户特征预计算表
CREATE TABLE user_feature_vectors (
    user_id VARCHAR(36) PRIMARY KEY REFERENCES users(id),
    feature_vector JSONB NOT NULL,
    category_preferences JSONB DEFAULT '{}',
    price_preferences JSONB DEFAULT '{}',
    location_preferences JSONB DEFAULT '{}',
    
    last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_feature_vector CHECK (jsonb_typeof(feature_vector) = 'object')
);

-- 创建GIN索引支持特征向量查询
CREATE INDEX idx_user_feature_vectors_features ON user_feature_vectors USING GIN (feature_vector);
CREATE INDEX idx_user_feature_vectors_categories ON user_feature_vectors USING GIN (category_preferences);
```

## 7. 效果评估

### 7.1 推荐效果指标

#### 7.1.1 指标计算服务
```kotlin
@Service
class RecommendationMetricsService(
    private val recommendationResultRepository: RecommendationResultRepository,
    private val recommendationFeedbackRepository: RecommendationFeedbackRepository
) {
    
    suspend fun calculateMetrics(startDate: LocalDate, endDate: LocalDate): RecommendationMetrics {
        val recommendations = recommendationResultRepository.findByDateRange(startDate, endDate)
        val feedbacks = recommendationFeedbackRepository.findByDateRange(startDate, endDate)
        
        // 点击率 (CTR)
        val totalRecommendations = recommendations.size
        val clickedRecommendations = recommendations.count { it.status == RecommendationStatus.CLICKED }
        val ctr = if (totalRecommendations > 0) clickedRecommendations.toDouble() / totalRecommendations else 0.0
        
        // 转化率
        val conversions = calculateConversions(recommendations)
        val conversionRate = if (clickedRecommendations > 0) conversions.toDouble() / clickedRecommendations else 0.0
        
        // 用户满意度
        val satisfactionScore = calculateSatisfactionScore(feedbacks)
        
        // 覆盖率
        val coverage = calculateCoverage(recommendations)
        
        // 多样性
        val diversity = calculateDiversity(recommendations)
        
        return RecommendationMetrics(
            ctr = ctr,
            conversionRate = conversionRate,
            satisfactionScore = satisfactionScore,
            coverage = coverage,
            diversity = diversity,
            totalRecommendations = totalRecommendations,
            period = DateRange(startDate, endDate)
        )
    }
    
    private suspend fun calculateConversions(recommendations: List<RecommendationResult>): Int {
        // 计算推荐后实际产生的交易数量
        return recommendations.count { rec ->
            // 检查是否在推荐后产生了相关的投标或交易
            hasSubsequentInteraction(rec.userId, rec.targetId, rec.createdAt)
        }
    }
    
    private fun calculateSatisfactionScore(feedbacks: List<RecommendationFeedback>): Double {
        if (feedbacks.isEmpty()) return 0.0
        
        val positiveWeight = 1.0
        val negativeWeight = -1.0
        val neutralWeight = 0.0
        
        val totalScore = feedbacks.sumOf { feedback ->
            when (feedback.feedbackType) {
                FeedbackType.LIKE, FeedbackType.HELPFUL -> positiveWeight
                FeedbackType.DISLIKE, FeedbackType.IRRELEVANT -> negativeWeight
                else -> neutralWeight
            }
        }
        
        return totalScore / feedbacks.size
    }
}

data class RecommendationMetrics(
    val ctr: Double,
    val conversionRate: Double,
    val satisfactionScore: Double,
    val coverage: Double,
    val diversity: Double,
    val totalRecommendations: Int,
    val period: DateRange
)
```

## 8. 总结

### 8.1 系统优势

#### **智能匹配**
- **多维度匹配**：类别、价格、地理位置、时间等多维度精准匹配
- **个性化推荐**：基于用户行为和偏好的个性化推荐
- **实时更新**：用户行为实时反馈，推荐结果动态调整

#### **技术先进性**
- **混合推荐算法**：内容推荐 + 协同过滤 + 热门推荐的有机结合
- **高性能架构**：Redis缓存 + 数据库优化 + 异步处理
- **可扩展设计**：模块化设计，支持算法扩展和性能优化

#### **业务价值**
- **提升效率**：供应商获得精准商机，买家获得优质供应商
- **增加粘性**：个性化体验提升用户满意度和平台粘性
- **数据驱动**：全面的效果评估和持续优化机制

这个智能推荐系统为采购平台提供了强大的供需匹配能力，显著提升了平台的商业价值。
