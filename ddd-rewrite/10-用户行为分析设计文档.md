# 用户行为分析系统设计

## 1. 系统概述

### 1.1 设计目标
构建全方位的用户行为分析系统，通过实时数据收集、智能分析和预测，为平台运营、个性化推荐、风险控制提供数据支撑。

### 1.2 核心价值
- **运营价值**：深度了解用户行为，优化产品和服务
- **个性化价值**：基于行为数据提供精准的个性化体验
- **风控价值**：识别异常行为，降低平台风险
- **商业价值**：挖掘用户价值，提升转化率和留存率

## 2. 行为数据模型

### 2.1 用户行为分类
```mermaid
graph TB
    subgraph "浏览行为"
        A1[页面访问] --> A2[内容浏览]
        A2 --> A3[搜索行为]
        A3 --> A4[筛选操作]
    end
    
    subgraph "交互行为"
        B1[点击行为] --> B2[收藏关注]
        B2 --> B3[分享转发]
        B3 --> B4[评论反馈]
    end
    
    subgraph "交易行为"
        C1[竞价提交] --> C2[订单创建]
        C2 --> C3[支付行为]
        C3 --> C4[履约行为]
    end
    
    subgraph "社交行为"
        D1[消息发送] --> D2[群组参与]
        D2 --> D3[内容发布]
        D3 --> D4[互动回复]
    end
    
    subgraph "系统行为"
        E1[登录登出] --> E2[设置修改]
        E2 --> E3[权限操作]
        E3 --> E4[异常行为]
    end
```

### 2.2 行为数据结构
```java
// 用户行为事件基础模型
@Entity
public class UserBehaviorEvent {
    private String eventId;
    private UserId userId;
    private String sessionId;
    private BehaviorType behaviorType;
    private String actionName;
    private LocalDateTime timestamp;
    private String pageUrl;
    private String referrerUrl;
    private String userAgent;
    private String ipAddress;
    private Map<String, Object> properties;
    private Map<String, Object> contextInfo;
    
    // 行为分类枚举
    public enum BehaviorType {
        BROWSE,     // 浏览行为
        INTERACT,   // 交互行为
        TRANSACT,   // 交易行为
        SOCIAL,     // 社交行为
        SYSTEM      // 系统行为
    }
}

// 具体行为事件示例
@Entity
public class RequirementViewEvent extends UserBehaviorEvent {
    private RequirementId requirementId;
    private String productCategory;
    private BigDecimal budgetRange;
    private Duration viewDuration;
    private Integer scrollDepth;
    private List<String> viewedSections;
}

@Entity
public class BidSubmissionEvent extends UserBehaviorEvent {
    private RequirementId requirementId;
    private BigDecimal bidAmount;
    private Integer deliveryDays;
    private String bidStrategy; // aggressive, conservative, competitive
    private Duration preparationTime;
}
```

## 3. 实时行为追踪

### 3.1 行为数据收集架构
```mermaid
graph LR
    subgraph "数据源"
        A[Web前端] --> D[数据收集层]
        B[移动端] --> D
        C[API调用] --> D
    end
    
    subgraph "数据收集层"
        D --> E[行为事件队列]
        E --> F[数据清洗]
        F --> G[数据验证]
    end
    
    subgraph "数据存储层"
        G --> H[实时数据库]
        G --> I[历史数据仓库]
        G --> J[行为索引]
    end
    
    subgraph "分析处理层"
        H --> K[实时分析引擎]
        I --> L[批量分析引擎]
        J --> M[查询分析引擎]
    end
    
    subgraph "应用层"
        K --> N[实时推荐]
        L --> O[用户画像]
        M --> P[行为报告]
    end
```

### 3.2 实时行为追踪服务
```java
// 行为追踪服务
@Component
public class BehaviorTrackingService {
    
    private final KafkaTemplate<String, UserBehaviorEvent> kafkaTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    
    public void trackBehavior(UserBehaviorEvent event) {
        // 1. 数据验证和清洗
        validateAndCleanEvent(event);
        
        // 2. 实时数据存储
        storeRealTimeData(event);
        
        // 3. 发送到消息队列进行异步处理
        kafkaTemplate.send("user-behavior-events", event.getUserId().toString(), event);
        
        // 4. 触发实时分析
        triggerRealTimeAnalysis(event);
    }
    
    private void storeRealTimeData(UserBehaviorEvent event) {
        String key = "user_behavior:" + event.getUserId() + ":" + 
                    LocalDate.now().toString();
        
        // 存储到Redis用于实时查询
        redisTemplate.opsForList().leftPush(key, event);
        redisTemplate.expire(key, Duration.ofDays(7)); // 7天过期
        
        // 更新用户会话信息
        updateUserSession(event);
    }
    
    private void updateUserSession(UserBehaviorEvent event) {
        String sessionKey = "user_session:" + event.getSessionId();
        
        UserSession session = (UserSession) redisTemplate.opsForValue().get(sessionKey);
        if (session == null) {
            session = new UserSession(event.getSessionId(), event.getUserId());
        }
        
        session.addBehaviorEvent(event);
        session.updateLastActivity(event.getTimestamp());
        
        redisTemplate.opsForValue().set(sessionKey, session, Duration.ofHours(2));
    }
    
    private void triggerRealTimeAnalysis(UserBehaviorEvent event) {
        // 实时异常检测
        if (isAnomalousBehavior(event)) {
            alertService.sendAnomalyAlert(event);
        }
        
        // 实时推荐触发
        if (shouldTriggerRecommendation(event)) {
            recommendationService.generateRealTimeRecommendation(event.getUserId());
        }
        
        // 实时营销触发
        if (shouldTriggerMarketing(event)) {
            marketingService.triggerRealTimeMarketing(event.getUserId(), event);
        }
    }
}
```

## 4. 用户画像构建

### 4.1 用户画像模型
```java
// 用户画像聚合根
@Entity
public class UserBehaviorProfile {
    private UserId userId;
    private LocalDateTime lastUpdated;
    
    // 基础画像
    private UserBasicProfile basicProfile;
    
    // 行为画像
    private BehaviorCharacteristics behaviorCharacteristics;
    
    // 偏好画像
    private UserPreferences preferences;
    
    // 价值画像
    private UserValueProfile valueProfile;
    
    // 风险画像
    private RiskProfile riskProfile;
    
    public void updateProfile(List<UserBehaviorEvent> recentEvents) {
        // 更新行为特征
        this.behaviorCharacteristics = analyzeBehaviorCharacteristics(recentEvents);
        
        // 更新用户偏好
        this.preferences = extractUserPreferences(recentEvents);
        
        // 更新价值评估
        this.valueProfile = calculateUserValue(recentEvents);
        
        // 更新风险评估
        this.riskProfile = assessUserRisk(recentEvents);
        
        this.lastUpdated = LocalDateTime.now();
    }
}

// 行为特征分析
@ValueObject
public class BehaviorCharacteristics {
    private ActivityLevel activityLevel;        // 活跃度
    private BrowsingPattern browsingPattern;    // 浏览模式
    private DecisionSpeed decisionSpeed;        // 决策速度
    private Pricesensitivity priceSensitivity; // 价格敏感度
    private QualityFocus qualityFocus;         // 质量关注度
    private TimePreference timePreference;     // 时间偏好
    private CommunicationStyle communicationStyle; // 沟通风格
    
    public static BehaviorCharacteristics analyze(List<UserBehaviorEvent> events) {
        return BehaviorCharacteristics.builder()
            .activityLevel(calculateActivityLevel(events))
            .browsingPattern(analyzeBrowsingPattern(events))
            .decisionSpeed(calculateDecisionSpeed(events))
            .priceSensitivity(analyzePriceSensitivity(events))
            .qualityFocus(analyzeQualityFocus(events))
            .timePreference(analyzeTimePreference(events))
            .communicationStyle(analyzeCommunicationStyle(events))
            .build();
    }
}
```

### 4.2 用户画像更新服务
```java
// 用户画像更新服务
@Service
public class UserProfileUpdateService {
    
    @EventListener
    public void handleBehaviorEvent(UserBehaviorEvent event) {
        // 异步更新用户画像
        CompletableFuture.runAsync(() -> updateUserProfile(event.getUserId()));
    }
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点批量更新
    public void batchUpdateUserProfiles() {
        List<UserId> activeUsers = getActiveUsersInLastWeek();
        
        activeUsers.parallelStream().forEach(userId -> {
            try {
                updateUserProfile(userId);
            } catch (Exception e) {
                log.error("Failed to update user profile for user: {}", userId, e);
            }
        });
    }
    
    private void updateUserProfile(UserId userId) {
        // 1. 获取最近的行为数据
        List<UserBehaviorEvent> recentEvents = behaviorRepository
            .findRecentEventsByUserId(userId, Duration.ofDays(30));
        
        // 2. 获取或创建用户画像
        UserBehaviorProfile profile = profileRepository.findByUserId(userId)
            .orElse(new UserBehaviorProfile(userId));
        
        // 3. 更新画像
        profile.updateProfile(recentEvents);
        
        // 4. 保存画像
        profileRepository.save(profile);
        
        // 5. 发布画像更新事件
        eventPublisher.publish(new UserProfileUpdatedEvent(userId, profile));
    }
}
```

## 5. 行为预测模型

### 5.1 用户行为预测
```java
// 行为预测服务
@Service
public class BehaviorPredictionService {
    
    private final MLModelService mlModelService;
    
    public BehaviorPrediction predictUserBehavior(UserId userId) {
        // 1. 获取用户历史行为特征
        UserBehaviorProfile profile = profileRepository.findByUserId(userId);
        BehaviorFeatures features = extractBehaviorFeatures(profile);
        
        // 2. 预测下一步行为
        NextActionPrediction nextAction = predictNextAction(features);
        
        // 3. 预测转化概率
        ConversionProbability conversionProb = predictConversionProbability(features);
        
        // 4. 预测流失风险
        ChurnRisk churnRisk = predictChurnRisk(features);
        
        // 5. 预测价值潜力
        ValuePotential valuePotential = predictValuePotential(features);
        
        return new BehaviorPrediction(
            userId, nextAction, conversionProb, churnRisk, valuePotential
        );
    }
    
    private NextActionPrediction predictNextAction(BehaviorFeatures features) {
        // 使用序列模型预测下一步行为
        float[] predictions = mlModelService.predict("next_action_model", features.toArray());
        
        Map<String, Float> actionProbabilities = new HashMap<>();
        actionProbabilities.put("browse_requirements", predictions[0]);
        actionProbabilities.put("submit_bid", predictions[1]);
        actionProbabilities.put("view_messages", predictions[2]);
        actionProbabilities.put("update_profile", predictions[3]);
        actionProbabilities.put("logout", predictions[4]);
        
        String mostLikelyAction = actionProbabilities.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("unknown");
            
        return new NextActionPrediction(mostLikelyAction, actionProbabilities);
    }
    
    private ConversionProbability predictConversionProbability(BehaviorFeatures features) {
        float conversionScore = mlModelService.predict("conversion_model", features.toArray())[0];
        
        ConversionLevel level;
        if (conversionScore > 0.8) level = ConversionLevel.HIGH;
        else if (conversionScore > 0.5) level = ConversionLevel.MEDIUM;
        else level = ConversionLevel.LOW;
        
        return new ConversionProbability(conversionScore, level);
    }
}
```

## 6. 异常行为检测

### 6.1 异常检测算法
```java
// 异常行为检测服务
@Service
public class BehaviorAnomalyDetectionService {
    
    public AnomalyDetectionResult detectAnomalies(UserId userId, UserBehaviorEvent event) {
        List<AnomalyIndicator> anomalies = new ArrayList<>();
        
        // 1. 频率异常检测
        FrequencyAnomalyResult frequencyAnomaly = detectFrequencyAnomaly(userId, event);
        if (frequencyAnomaly.isAnomalous()) {
            anomalies.add(new AnomalyIndicator(
                AnomalyType.FREQUENCY_ANOMALY,
                frequencyAnomaly.getAnomalyScore(),
                "用户行为频率异常：" + frequencyAnomaly.getDescription()
            ));
        }
        
        // 2. 模式异常检测
        PatternAnomalyResult patternAnomaly = detectPatternAnomaly(userId, event);
        if (patternAnomaly.isAnomalous()) {
            anomalies.add(new AnomalyIndicator(
                AnomalyType.PATTERN_ANOMALY,
                patternAnomaly.getAnomalyScore(),
                "用户行为模式异常：" + patternAnomaly.getDescription()
            ));
        }
        
        // 3. 设备异常检测
        DeviceAnomalyResult deviceAnomaly = detectDeviceAnomaly(userId, event);
        if (deviceAnomaly.isAnomalous()) {
            anomalies.add(new AnomalyIndicator(
                AnomalyType.DEVICE_ANOMALY,
                deviceAnomaly.getAnomalyScore(),
                "设备使用异常：" + deviceAnomaly.getDescription()
            ));
        }
        
        // 4. 地理位置异常检测
        LocationAnomalyResult locationAnomaly = detectLocationAnomaly(userId, event);
        if (locationAnomaly.isAnomalous()) {
            anomalies.add(new AnomalyIndicator(
                AnomalyType.LOCATION_ANOMALY,
                locationAnomaly.getAnomalyScore(),
                "地理位置异常：" + locationAnomaly.getDescription()
            ));
        }
        
        return new AnomalyDetectionResult(anomalies);
    }
    
    private FrequencyAnomalyResult detectFrequencyAnomaly(UserId userId, UserBehaviorEvent event) {
        // 获取用户最近的行为频率
        Duration timeWindow = Duration.ofHours(1);
        List<UserBehaviorEvent> recentEvents = behaviorRepository
            .findEventsByUserIdAndTimeRange(userId, 
                event.getTimestamp().minus(timeWindow), 
                event.getTimestamp());
        
        // 计算当前行为类型的频率
        long currentTypeCount = recentEvents.stream()
            .filter(e -> e.getBehaviorType().equals(event.getBehaviorType()))
            .count();
        
        // 获取历史平均频率
        double historicalAverage = getHistoricalAverageFrequency(userId, event.getBehaviorType());
        double standardDeviation = getHistoricalStandardDeviation(userId, event.getBehaviorType());
        
        // 计算Z-score
        double zScore = (currentTypeCount - historicalAverage) / standardDeviation;
        
        // 判断是否异常（Z-score > 3 认为异常）
        boolean isAnomalous = Math.abs(zScore) > 3.0;
        double anomalyScore = Math.min(Math.abs(zScore) / 3.0, 1.0);
        
        return new FrequencyAnomalyResult(
            isAnomalous, 
            anomalyScore, 
            String.format("行为频率异常，Z-score: %.2f", zScore)
        );
    }
}
```

这套用户行为分析系统设计提供了完整的用户行为追踪、分析、预测和异常检测能力，为平台的个性化服务、风险控制和运营优化提供了强大的数据支撑。
