# 用户信用和会员体系设计 (Kotlin + PostgreSQL + Hibernate + JPA)

## 1. 体系概述

### 1.1 设计目标
构建完整的用户信用评估和会员等级管理体系，通过差异化服务和激励机制，提升用户忠诚度和平台价值。

### 1.2 核心价值
- **用户价值**：通过良好行为获得更高等级和更多权益
- **平台价值**：提升用户粘性，降低风险，增加收入
- **生态价值**：建立信任机制，促进高质量交易

## 2. 用户信用评分系统

### 2.1 信用评分模型
```mermaid
graph TB
    subgraph "信用评分维度"
        A[交易履约 40%] --> G[综合信用评分]
        B[资质认证 20%] --> G
        C[平台活跃度 15%] --> G
        D[用户反馈 15%] --> G
        E[财务状况 10%] --> G
    end
    
    subgraph "交易履约细分"
        A --> A1[按时交付率]
        A --> A2[质量合格率]
        A --> A3[合同履行率]
        A --> A4[售后服务质量]
    end
    
    subgraph "资质认证细分"
        B --> B1[实名认证]
        B --> B2[企业认证]
        B --> B3[行业资质]
        B --> B4[第三方认证]
    end
    
    subgraph "信用等级划分"
        G --> H{信用分数}
        H -->|850-1000| I[AAA级 优秀]
        H -->|750-849| J[AA级 良好]
        H -->|650-749| K[A级 一般]
        H -->|550-649| L[B级 较差]
        H -->|0-549| M[C级 风险]
    end
```

### 2.2 数据模型设计

#### 2.2.1 信用评分表设计
```sql
-- 用户信用评分表
CREATE TABLE user_credit_scores (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    
    -- 综合评分
    total_score INTEGER NOT NULL DEFAULT 600 CHECK (total_score >= 0 AND total_score <= 1000),
    credit_level VARCHAR(10) NOT NULL DEFAULT 'B' CHECK (credit_level IN ('AAA', 'AA', 'A', 'B', 'C')),
    
    -- 各维度评分
    fulfillment_score INTEGER DEFAULT 0 CHECK (fulfillment_score >= 0 AND fulfillment_score <= 400),
    certification_score INTEGER DEFAULT 0 CHECK (certification_score >= 0 AND certification_score <= 200),
    activity_score INTEGER DEFAULT 0 CHECK (activity_score >= 0 AND activity_score <= 150),
    feedback_score INTEGER DEFAULT 0 CHECK (feedback_score >= 0 AND feedback_score <= 150),
    financial_score INTEGER DEFAULT 0 CHECK (financial_score >= 0 AND financial_score <= 100),
    
    -- 评分详情
    score_details JSONB DEFAULT '{}',
    
    -- 时间信息
    last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id)
);

-- 信用评分历史表
CREATE TABLE credit_score_histories (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    old_score INTEGER NOT NULL,
    new_score INTEGER NOT NULL,
    old_level VARCHAR(10) NOT NULL,
    new_level VARCHAR(10) NOT NULL,
    change_reason VARCHAR(255),
    change_details JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引设计
CREATE INDEX idx_user_credit_scores_user_id ON user_credit_scores(user_id);
CREATE INDEX idx_user_credit_scores_level ON user_credit_scores(credit_level);
CREATE INDEX idx_user_credit_scores_total_score ON user_credit_scores(total_score DESC);
CREATE INDEX idx_credit_score_histories_user_id ON credit_score_histories(user_id);
CREATE INDEX idx_credit_score_histories_created_at ON credit_score_histories(created_at);
```

#### 2.2.2 Kotlin实体映射
```kotlin
@Entity
@Table(name = "user_credit_scores")
data class UserCreditScore(
    @Id
    val id: CreditScoreId,
    
    @Column(name = "user_id", unique = true)
    val userId: UserId,
    
    @Column(name = "total_score")
    var totalScore: Int = 600,
    
    @Enumerated(EnumType.STRING)
    @Column(name = "credit_level")
    var creditLevel: CreditLevel = CreditLevel.B,
    
    @Column(name = "fulfillment_score")
    var fulfillmentScore: Int = 0,
    
    @Column(name = "certification_score")
    var certificationScore: Int = 0,
    
    @Column(name = "activity_score")
    var activityScore: Int = 0,
    
    @Column(name = "feedback_score")
    var feedbackScore: Int = 0,
    
    @Column(name = "financial_score")
    var financialScore: Int = 0,
    
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "score_details", columnDefinition = "jsonb")
    var scoreDetails: CreditScoreDetails = CreditScoreDetails(),
    
    @Column(name = "last_calculated_at")
    var lastCalculatedAt: Instant = Instant.now(),
    
    @Column(name = "created_at")
    val createdAt: Instant = Instant.now(),
    
    @Column(name = "updated_at")
    var updatedAt: Instant = Instant.now()
) {
    
    fun updateScore(newScores: CreditScoreComponents): UserCreditScore = apply {
        val oldTotal = totalScore
        val oldLevel = creditLevel
        
        fulfillmentScore = newScores.fulfillment
        certificationScore = newScores.certification
        activityScore = newScores.activity
        feedbackScore = newScores.feedback
        financialScore = newScores.financial
        
        totalScore = fulfillmentScore + certificationScore + activityScore + feedbackScore + financialScore
        creditLevel = calculateCreditLevel(totalScore)
        lastCalculatedAt = Instant.now()
        updatedAt = Instant.now()
        
        // 记录变更历史
        if (oldTotal != totalScore || oldLevel != creditLevel) {
            addDomainEvent(CreditScoreUpdatedEvent(userId, oldTotal, totalScore, oldLevel, creditLevel))
        }
    }
    
    private fun calculateCreditLevel(score: Int): CreditLevel = when {
        score >= 850 -> CreditLevel.AAA
        score >= 750 -> CreditLevel.AA
        score >= 650 -> CreditLevel.A
        score >= 550 -> CreditLevel.B
        else -> CreditLevel.C
    }
}

enum class CreditLevel {
    AAA, AA, A, B, C
}

data class CreditScoreComponents(
    val fulfillment: Int,
    val certification: Int,
    val activity: Int,
    val feedback: Int,
    val financial: Int
)
```

### 2.3 信用评分计算服务

#### 2.3.1 信用评分计算引擎
```kotlin
@Service
class CreditEvaluationService(
    private val userRepository: UserRepository,
    private val orderRepository: OrderRepository,
    private val userBehaviorRepository: UserBehaviorRepository,
    private val creditScoreRepository: CreditScoreRepository
) {
    
    suspend fun calculateCreditScore(userId: UserId): CreditScore = coroutineScope {
        val user = userRepository.findById(userId)
            ?: throw UserNotFoundException("用户不存在: $userId")

        // 并发计算各维度评分
        val fulfillmentScore = async { calculateFulfillmentScore(userId) }
        val certificationScore = async { calculateCertificationScore(userId) }
        val activityScore = async { calculateActivityScore(userId) }
        val feedbackScore = async { calculateFeedbackScore(userId) }
        val financialScore = async { calculateFinancialScore(userId) }

        // 等待所有计算完成
        val scores = awaitAll(
            fulfillmentScore, certificationScore, activityScore,
            feedbackScore, financialScore
        )

        // 综合评分计算
        val totalScore = scores[0] * 0.4 +
                        scores[1] * 0.2 +
                        scores[2] * 0.15 +
                        scores[3] * 0.15 +
                        scores[4] * 0.1

        CreditScore(
            userId = userId,
            totalScore = totalScore.toInt(),
            fulfillmentScore = scores[0],
            certificationScore = scores[1],
            activityScore = scores[2],
            feedbackScore = scores[3],
            financialScore = scores[4]
        )
    }
    
    private suspend fun calculateFulfillmentScore(userId: UserId): Int {
        val orders = orderRepository.findCompletedOrdersByUserId(userId)
        if (orders.isEmpty()) return 0
        
        // 按时交付率 (25%)
        val onTimeDeliveryRate = orders.count { it.isDeliveredOnTime() }.toDouble() / orders.size
        val onTimeScore = (onTimeDeliveryRate * 100).toInt()
        
        // 质量合格率 (25%)
        val qualityPassRate = orders.count { it.hasGoodQuality() }.toDouble() / orders.size
        val qualityScore = (qualityPassRate * 100).toInt()
        
        // 合同履行率 (25%)
        val contractFulfillmentRate = orders.count { it.isContractFulfilled() }.toDouble() / orders.size
        val contractScore = (contractFulfillmentRate * 100).toInt()
        
        // 售后服务质量 (25%)
        val serviceQualityScore = calculateServiceQualityScore(orders)
        
        return ((onTimeScore + qualityScore + contractScore + serviceQualityScore) * 0.4).toInt()
    }
    
    private suspend fun calculateCertificationScore(userId: UserId): Int {
        val user = userRepository.findById(userId) ?: return 0
        var score = 0
        
        // 实名认证 (50分)
        if (user.profile.isRealNameVerified) score += 50
        
        // 企业认证 (100分)
        if (user.profile.isCompanyVerified) score += 100
        
        // 行业资质认证 (30分)
        if (user.profile.hasIndustryQualification) score += 30
        
        // 第三方认证 (20分)
        if (user.profile.hasThirdPartyVerification) score += 20
        
        return score
    }
    
    private suspend fun calculateActivityScore(userId: UserId): Int {
        val behaviors = userBehaviorRepository.findRecentBehaviors(userId, Duration.ofDays(90))
        
        // 登录频率 (40分)
        val loginFrequency = calculateLoginFrequency(behaviors)
        val loginScore = minOf(40, loginFrequency * 2)
        
        // 交易频率 (60分)
        val transactionFrequency = calculateTransactionFrequency(userId)
        val transactionScore = minOf(60, transactionFrequency * 10)
        
        // 平台互动 (50分)
        val interactionScore = calculateInteractionScore(behaviors)
        val finalInteractionScore = minOf(50, interactionScore)
        
        return loginScore + transactionScore + finalInteractionScore
    }
    
    private suspend fun calculateFeedbackScore(userId: UserId): Int {
        val feedbacks = getFeedbacksForUser(userId)
        if (feedbacks.isEmpty()) return 75 // 默认中等分数
        
        val averageRating = feedbacks.map { it.rating }.average()
        val feedbackCount = feedbacks.size
        
        // 基础评分 (基于平均评分)
        val baseScore = (averageRating * 20).toInt() // 5星制转100分制
        
        // 评价数量加成 (最多30分)
        val countBonus = minOf(30, feedbackCount * 2)
        
        return minOf(150, baseScore + countBonus)
    }
    
    private suspend fun calculateFinancialScore(userId: UserId): Int {
        val financialData = getFinancialDataForUser(userId)
        
        // 交易金额 (40分)
        val transactionAmountScore = calculateTransactionAmountScore(financialData.totalTransactionAmount)
        
        // 资金流水 (30分)
        val cashFlowScore = calculateCashFlowScore(financialData.monthlyAverageCashFlow)
        
        // 信用记录 (30分)
        val creditHistoryScore = calculateCreditHistoryScore(financialData.creditHistory)
        
        return transactionAmountScore + cashFlowScore + creditHistoryScore
    }
}
```

## 3. 会员等级体系

### 3.1 会员等级设计

#### 3.1.1 会员等级表设计
```sql
-- 会员等级配置表
CREATE TABLE membership_tiers (
    id VARCHAR(36) PRIMARY KEY,
    tier_name VARCHAR(50) NOT NULL UNIQUE,
    tier_level INTEGER NOT NULL UNIQUE,
    min_credit_score INTEGER NOT NULL,
    min_transaction_amount DECIMAL(19,2) DEFAULT 0,
    min_transaction_count INTEGER DEFAULT 0,
    
    -- 权益配置
    benefits JSONB NOT NULL DEFAULT '{}',
    
    -- 等级描述
    description TEXT,
    tier_color VARCHAR(20),
    tier_icon VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    is_active BOOLEAN DEFAULT true
);

-- 用户会员等级表
CREATE TABLE user_memberships (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    tier_id VARCHAR(36) NOT NULL REFERENCES membership_tiers(id),
    
    -- 等级信息
    current_tier_level INTEGER NOT NULL,
    tier_name VARCHAR(50) NOT NULL,
    
    -- 升级条件达成情况
    current_credit_score INTEGER NOT NULL,
    current_transaction_amount DECIMAL(19,2) DEFAULT 0,
    current_transaction_count INTEGER DEFAULT 0,
    
    -- 时间信息
    tier_start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    tier_end_date TIMESTAMP WITH TIME ZONE,
    last_evaluated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id)
);

-- 会员等级变更历史表
CREATE TABLE membership_tier_histories (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    old_tier_level INTEGER,
    new_tier_level INTEGER NOT NULL,
    old_tier_name VARCHAR(50),
    new_tier_name VARCHAR(50) NOT NULL,
    
    change_reason VARCHAR(255),
    change_details JSONB DEFAULT '{}',
    
    effective_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引设计
CREATE INDEX idx_membership_tiers_level ON membership_tiers(tier_level);
CREATE INDEX idx_user_memberships_user_id ON user_memberships(user_id);
CREATE INDEX idx_user_memberships_tier_level ON user_memberships(current_tier_level);
CREATE INDEX idx_membership_tier_histories_user_id ON membership_tier_histories(user_id);
```

#### 3.1.2 会员权益配置
```sql
-- 插入会员等级配置
INSERT INTO membership_tiers (id, tier_name, tier_level, min_credit_score, min_transaction_amount, min_transaction_count, benefits, description, tier_color) VALUES
('tier-1', '青铜会员', 1, 0, 0, 0, '{"commission_discount": 0, "priority_support": false, "advanced_features": false}', '新用户默认等级', '#CD7F32'),
('tier-2', '白银会员', 2, 650, 50000, 5, '{"commission_discount": 0.05, "priority_support": false, "advanced_features": true}', '活跃用户等级', '#C0C0C0'),
('tier-3', '黄金会员', 3, 750, 200000, 20, '{"commission_discount": 0.1, "priority_support": true, "advanced_features": true}', '优质用户等级', '#FFD700'),
('tier-4', '铂金会员', 4, 850, 500000, 50, '{"commission_discount": 0.15, "priority_support": true, "advanced_features": true, "dedicated_manager": true}', '顶级用户等级', '#E5E4E2'),
('tier-5', '钻石会员', 5, 950, 1000000, 100, '{"commission_discount": 0.2, "priority_support": true, "advanced_features": true, "dedicated_manager": true, "custom_solutions": true}', '至尊用户等级', '#B9F2FF');
```

### 3.2 会员等级评估服务

#### 3.2.1 会员等级计算
```kotlin
@Service
class MembershipService(
    private val membershipTierRepository: MembershipTierRepository,
    private val userMembershipRepository: UserMembershipRepository,
    private val creditEvaluationService: CreditEvaluationService,
    private val orderRepository: OrderRepository
) {
    
    suspend fun evaluateMembershipTier(userId: UserId): MembershipTier {
        // 1. 获取用户当前数据
        val creditScore = creditEvaluationService.getCurrentCreditScore(userId)
        val transactionData = getTransactionData(userId)
        
        // 2. 获取所有等级配置
        val allTiers = membershipTierRepository.findAllActiveTiers()
            .sortedByDescending { it.tierLevel }
        
        // 3. 找到符合条件的最高等级
        val qualifiedTier = allTiers.firstOrNull { tier ->
            creditScore.totalScore >= tier.minCreditScore &&
            transactionData.totalAmount >= tier.minTransactionAmount &&
            transactionData.transactionCount >= tier.minTransactionCount
        } ?: allTiers.last() // 默认最低等级
        
        return qualifiedTier
    }
    
    suspend fun upgradeMembershipIfEligible(userId: UserId): MembershipUpgradeResult {
        val currentMembership = userMembershipRepository.findByUserId(userId)
        val newTier = evaluateMembershipTier(userId)
        
        return if (currentMembership == null || newTier.tierLevel > currentMembership.currentTierLevel) {
            // 执行升级
            val upgradedMembership = upgradeMembership(userId, newTier)
            MembershipUpgradeResult.Success(upgradedMembership, newTier)
        } else {
            MembershipUpgradeResult.NoUpgrade(currentMembership)
        }
    }
    
    private suspend fun upgradeMembership(userId: UserId, newTier: MembershipTier): UserMembership {
        val currentMembership = userMembershipRepository.findByUserId(userId)
        val transactionData = getTransactionData(userId)
        val creditScore = creditEvaluationService.getCurrentCreditScore(userId)
        
        val newMembership = if (currentMembership != null) {
            // 更新现有会员等级
            currentMembership.copy(
                tierId = newTier.id,
                currentTierLevel = newTier.tierLevel,
                tierName = newTier.tierName,
                currentCreditScore = creditScore.totalScore,
                currentTransactionAmount = transactionData.totalAmount,
                currentTransactionCount = transactionData.transactionCount,
                lastEvaluatedAt = Instant.now(),
                updatedAt = Instant.now()
            )
        } else {
            // 创建新的会员记录
            UserMembership.create(
                userId = userId,
                tier = newTier,
                creditScore = creditScore.totalScore,
                transactionAmount = transactionData.totalAmount,
                transactionCount = transactionData.transactionCount
            )
        }
        
        // 保存会员等级变更
        userMembershipRepository.save(newMembership)
        
        // 记录等级变更历史
        if (currentMembership != null && currentMembership.currentTierLevel != newTier.tierLevel) {
            recordTierChange(userId, currentMembership.currentTierLevel, newTier.tierLevel)
        }
        
        // 发布会员升级事件
        publishMembershipUpgradeEvent(userId, currentMembership?.currentTierLevel, newTier.tierLevel)
        
        return newMembership
    }
    
    private suspend fun getTransactionData(userId: UserId): TransactionData {
        val orders = orderRepository.findCompletedOrdersByUserId(userId)
        
        return TransactionData(
            totalAmount = orders.sumOf { it.totalAmount },
            transactionCount = orders.size,
            averageOrderValue = if (orders.isNotEmpty()) orders.sumOf { it.totalAmount } / orders.size else BigDecimal.ZERO
        )
    }
}

data class TransactionData(
    val totalAmount: BigDecimal,
    val transactionCount: Int,
    val averageOrderValue: BigDecimal
)

sealed class MembershipUpgradeResult {
    data class Success(val membership: UserMembership, val newTier: MembershipTier) : MembershipUpgradeResult()
    data class NoUpgrade(val currentMembership: UserMembership) : MembershipUpgradeResult()
}
```

## 4. 权益管理系统

### 4.1 权益配置和应用

#### 4.1.1 权益应用服务
```kotlin
@Service
class MembershipBenefitsService(
    private val userMembershipRepository: UserMembershipRepository,
    private val membershipTierRepository: MembershipTierRepository
) {
    
    suspend fun calculateCommissionDiscount(userId: UserId, originalCommission: BigDecimal): BigDecimal {
        val membership = userMembershipRepository.findByUserId(userId)
            ?: return originalCommission
        
        val tier = membershipTierRepository.findById(membership.tierId)
            ?: return originalCommission
        
        val discountRate = tier.benefits.commissionDiscount
        return originalCommission * (BigDecimal.ONE - BigDecimal.valueOf(discountRate))
    }
    
    suspend fun hasPrioritySupport(userId: UserId): Boolean {
        val membership = userMembershipRepository.findByUserId(userId)
            ?: return false
        
        val tier = membershipTierRepository.findById(membership.tierId)
            ?: return false
        
        return tier.benefits.prioritySupport
    }
    
    suspend fun hasAdvancedFeatures(userId: UserId): Boolean {
        val membership = userMembershipRepository.findByUserId(userId)
            ?: return false
        
        val tier = membershipTierRepository.findById(membership.tierId)
            ?: return false
        
        return tier.benefits.advancedFeatures
    }
    
    suspend fun getMembershipBenefits(userId: UserId): MembershipBenefits {
        val membership = userMembershipRepository.findByUserId(userId)
            ?: return MembershipBenefits.default()
        
        val tier = membershipTierRepository.findById(membership.tierId)
            ?: return MembershipBenefits.default()
        
        return MembershipBenefits(
            commissionDiscount = tier.benefits.commissionDiscount,
            prioritySupport = tier.benefits.prioritySupport,
            advancedFeatures = tier.benefits.advancedFeatures,
            dedicatedManager = tier.benefits.dedicatedManager ?: false,
            customSolutions = tier.benefits.customSolutions ?: false,
            tierName = tier.tierName,
            tierLevel = tier.tierLevel,
            tierColor = tier.tierColor
        )
    }
}

data class MembershipBenefits(
    val commissionDiscount: Double,
    val prioritySupport: Boolean,
    val advancedFeatures: Boolean,
    val dedicatedManager: Boolean,
    val customSolutions: Boolean,
    val tierName: String,
    val tierLevel: Int,
    val tierColor: String?
) {
    companion object {
        fun default() = MembershipBenefits(
            commissionDiscount = 0.0,
            prioritySupport = false,
            advancedFeatures = false,
            dedicatedManager = false,
            customSolutions = false,
            tierName = "青铜会员",
            tierLevel = 1,
            tierColor = "#CD7F32"
        )
    }
}
```

## 5. 定时评估和更新

### 5.1 自动化评估任务

#### 5.1.1 定时评估服务
```kotlin
@Component
class CreditAndMembershipEvaluationTask(
    private val creditEvaluationService: CreditEvaluationService,
    private val membershipService: MembershipService,
    private val userRepository: UserRepository,
    private val notificationService: NotificationService
) {
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    suspend fun evaluateAllUsers() {
        logger.info("Starting daily credit and membership evaluation")
        
        try {
            val activeUsers = userRepository.findActiveUsers()
            val batchSize = 100
            
            activeUsers.chunked(batchSize).forEach { userBatch ->
                coroutineScope {
                    userBatch.map { user ->
                        async {
                            evaluateUser(user.id)
                        }
                    }.awaitAll()
                }
            }
            
            logger.info("Completed evaluation for ${activeUsers.size} users")
            
        } catch (e: Exception) {
            logger.error("Failed to complete daily evaluation", e)
        }
    }
    
    private suspend fun evaluateUser(userId: UserId) {
        try {
            // 1. 重新计算信用分数
            val newCreditScore = creditEvaluationService.calculateCreditScore(userId)
            creditEvaluationService.updateCreditScore(userId, newCreditScore)
            
            // 2. 重新评估会员等级
            val upgradeResult = membershipService.upgradeMembershipIfEligible(userId)
            
            // 3. 发送通知（如果有升级）
            if (upgradeResult is MembershipUpgradeResult.Success) {
                notificationService.sendMembershipUpgradeNotification(
                    userId, 
                    upgradeResult.newTier
                )
            }
            
        } catch (e: Exception) {
            logger.error("Failed to evaluate user: $userId", e)
        }
    }
    
    companion object {
        private val logger = LoggerFactory.getLogger(CreditAndMembershipEvaluationTask::class.java)
    }
}
```

## 6. API接口设计

### 6.1 信用和会员API

#### 6.1.1 REST API实现
```kotlin
@RestController
@RequestMapping("/api/v1/credit-membership")
class CreditMembershipController(
    private val creditEvaluationService: CreditEvaluationService,
    private val membershipService: MembershipService,
    private val membershipBenefitsService: MembershipBenefitsService
) {
    
    @GetMapping("/credit-score/{userId}")
    suspend fun getCreditScore(@PathVariable userId: String): ResponseEntity<CreditScoreResponse> {
        val userIdObj = UserId(userId)
        val creditScore = creditEvaluationService.getCurrentCreditScore(userIdObj)
        
        val response = CreditScoreResponse(
            userId = userId,
            totalScore = creditScore.totalScore,
            creditLevel = creditScore.creditLevel.name,
            fulfillmentScore = creditScore.fulfillmentScore,
            certificationScore = creditScore.certificationScore,
            activityScore = creditScore.activityScore,
            feedbackScore = creditScore.feedbackScore,
            financialScore = creditScore.financialScore,
            lastCalculatedAt = creditScore.lastCalculatedAt
        )
        
        return ResponseEntity.ok(response)
    }
    
    @GetMapping("/membership/{userId}")
    suspend fun getMembership(@PathVariable userId: String): ResponseEntity<MembershipResponse> {
        val userIdObj = UserId(userId)
        val membership = membershipService.getCurrentMembership(userIdObj)
        val benefits = membershipBenefitsService.getMembershipBenefits(userIdObj)
        
        val response = MembershipResponse(
            userId = userId,
            tierName = membership.tierName,
            tierLevel = membership.currentTierLevel,
            tierColor = benefits.tierColor,
            benefits = benefits,
            tierStartDate = membership.tierStartDate,
            nextEvaluationDate = membership.lastEvaluatedAt.plus(Duration.ofDays(30))
        )
        
        return ResponseEntity.ok(response)
    }
    
    @PostMapping("/recalculate/{userId}")
    suspend fun recalculateCreditScore(@PathVariable userId: String): ResponseEntity<CreditScoreResponse> {
        val userIdObj = UserId(userId)
        val newCreditScore = creditEvaluationService.calculateCreditScore(userIdObj)
        creditEvaluationService.updateCreditScore(userIdObj, newCreditScore)
        
        // 检查会员等级是否需要更新
        membershipService.upgradeMembershipIfEligible(userIdObj)
        
        val response = CreditScoreResponse(
            userId = userId,
            totalScore = newCreditScore.totalScore,
            creditLevel = newCreditScore.creditLevel.name,
            fulfillmentScore = newCreditScore.fulfillmentScore,
            certificationScore = newCreditScore.certificationScore,
            activityScore = newCreditScore.activityScore,
            feedbackScore = newCreditScore.feedbackScore,
            financialScore = newCreditScore.financialScore,
            lastCalculatedAt = Instant.now()
        )
        
        return ResponseEntity.ok(response)
    }
}

data class CreditScoreResponse(
    val userId: String,
    val totalScore: Int,
    val creditLevel: String,
    val fulfillmentScore: Int,
    val certificationScore: Int,
    val activityScore: Int,
    val feedbackScore: Int,
    val financialScore: Int,
    val lastCalculatedAt: Instant
)

data class MembershipResponse(
    val userId: String,
    val tierName: String,
    val tierLevel: Int,
    val tierColor: String?,
    val benefits: MembershipBenefits,
    val tierStartDate: Instant,
    val nextEvaluationDate: Instant
)
```

## 7. 总结

### 7.1 系统优势

#### **科学的评估体系**
- **多维度评估**：交易履约、资质认证、平台活跃度、用户反馈、财务状况
- **动态调整**：基于实时数据持续更新信用评分
- **公平透明**：明确的评分规则和等级标准

#### **完善的激励机制**
- **差异化权益**：不同等级享受不同的平台权益
- **成长路径清晰**：用户可以明确了解升级条件
- **即时反馈**：行为改变立即反映在信用评分中

#### **技术实现优势**
- **高性能计算**：协程并发计算各维度评分
- **实时更新**：事件驱动的评分更新机制
- **可扩展性**：模块化设计支持新的评分维度

这个信用和会员体系为平台建立了完整的用户激励和风险控制机制，有效提升了用户粘性和平台价值。
