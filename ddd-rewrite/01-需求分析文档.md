# 采购系统DDD重写 - 业务需求分析（单体架构版）

## 1. 项目定位

### 1.1 战略定位
将现有采购系统重构为基于DDD的现代化单体应用，提升代码质量、可维护性和业务响应速度。

### 1.2 价值主张
- **开发效率**：清晰的领域边界，提升开发和维护效率
- **代码质量**：现代化技术栈，减少技术债务
- **业务响应**：灵活的领域模型，快速响应业务变化
- **系统稳定性**：单体架构的简单性，降低运维复杂度

### 1.3 发展方向
- **模块化**：清晰的模块边界，为未来微服务化做准备
- **现代化**：采用Kotlin和Spring Boot 3.x现代技术栈
- **智能化**：基于数据的智能推荐和分析（简化实现）

## 2. 业务领域分析

### 2.1 核心业务流程

#### 2.1.1 标准采购流程
```
采购需求发布 → 供应商竞价 → 管理员审核 → 采购商选择 → 生成订单
    ↓
自动生成物流需求 → 物流竞价 → 审核选择 → 生成物流订单 → 结算
    ↓
订单完成 → 建立供应商信任关系 → 支持一键补货
```

#### 2.1.2 简化补货流程
```
库存预警 → 推荐信任供应商 → 确认下单 → 直接执行
```

### 2.2 用户角色（简化版）
- **采购商**：发布需求、选择供应商、管理订单
- **供应商**：响应需求、提供报价、履行订单
- **物流商**：提供物流服务、运输货物
- **平台管理员**：审核、监控、客服支持

## 3. 功能需求（单体架构适配）

### 3.1 核心功能模块

#### 3.1.1 用户管理模块
- 用户注册、登录、权限管理
- 企业认证和信用评级
- 用户画像和行为分析（基于数据库实现）

#### 3.1.2 采购管理模块
- 采购需求发布和管理
- 供应商竞价和选择
- 订单生成和跟踪

#### 3.1.3 物流管理模块
- 物流需求自动生成
- 物流商竞价和选择
- 运输跟踪和管理

#### 3.1.4 财务结算模块
- 多方结算和分账
- 佣金计算和分配
- 财务报表和统计

#### 3.1.5 库存管理模块
- 库存录入和监控
- 基于信任关系的一键补货
- 智能预警和推荐

#### 3.1.6 智能推荐模块（简化版）
- 基于历史数据的供应商推荐
- 简单的协同过滤算法
- 推荐效果统计

### 3.2 非功能性需求

#### 3.2.1 性能需求
- **响应时间**：页面响应时间 < 2秒
- **并发用户**：支持1000并发用户
- **数据处理**：支持百万级订单数据

#### 3.2.2 可用性需求
- **系统可用性**：99.5%（单体架构目标）
- **故障恢复**：< 30分钟
- **数据备份**：每日自动备份

#### 3.2.3 安全需求
- **数据加密**：敏感数据加密存储
- **访问控制**：基于角色的权限控制
- **审计日志**：关键操作审计记录

## 4. 技术约束（单体架构）

### 4.1 架构约束
- **单体部署**：所有模块部署在同一应用中
- **数据库**：使用PostgreSQL作为主数据库
- **缓存**：Redis作为缓存和会话存储
- **事件**：Spring Events处理内部事件

### 4.2 技术栈约束
- **开发语言**：Kotlin 2.1.x
- **框架**：Spring Boot 3.5.4
- **ORM**：Hibernate 6.6.x + Spring Data JPA
- **构建工具**：Gradle 8.x

### 4.3 部署约束
- **容器化**：Docker容器部署
- **负载均衡**：Nginx反向代理
- **监控**：Spring Boot Actuator + 文件日志
- **备份**：数据库定期备份

## 5. 业务规则

### 5.1 采购业务规则
- 采购需求必须经过审核才能发布
- 供应商报价有效期不超过7天
- 订单金额超过10万需要额外审批

### 5.2 信用体系规则
- 新用户初始信用分60分
- 成功交易后信用分增加
- 违约行为导致信用分扣减
- 信用分影响一键补货权限

### 5.3 结算规则
- 订单完成后自动触发结算
- 平台佣金按交易金额的2-5%收取
- 结算周期为T+7工作日

## 6. 数据需求

### 6.1 主要数据实体
- **用户数据**：用户信息、企业信息、认证状态
- **采购数据**：需求、报价、订单、合同
- **物流数据**：运输信息、跟踪记录
- **财务数据**：交易记录、结算信息、佣金
- **行为数据**：用户行为、操作日志

### 6.2 数据量估算
- **用户数据**：10万用户
- **订单数据**：100万订单/年
- **行为数据**：1000万条/年
- **存储需求**：500GB（3年数据）

## 7. 集成需求

### 7.1 外部系统集成
- **支付系统**：支付宝、微信支付
- **物流系统**：顺丰、圆通等物流API
- **短信服务**：阿里云短信服务
- **文件存储**：阿里云OSS

### 7.2 内部系统集成
- **现有用户系统**：用户数据迁移
- **财务系统**：财务数据同步
- **客服系统**：工单和消息集成

这个需求分析专门针对单体架构进行了优化，去除了分布式系统的复杂性，专注于DDD在单体应用中的价值实现。
