# 采购系统DDD重写 - 单体DDD系统设计文档

## 1. 系统设计概述

### 1.1 设计原则
- **单体架构**：所有模块在同一进程中，通过清晰的包结构分离
- **DDD建模**：基于领域驱动设计，清晰的聚合边界
- **模块化设计**：为未来微服务化预留清晰的拆分边界
- **事件驱动**：使用Spring Events实现模块间解耦

### 1.2 架构目标
- **可维护性**：清晰的领域边界和职责分离
- **可扩展性**：模块化设计支持功能扩展
- **可演进性**：为未来微服务化做好准备
- **简单性**：避免过度设计，专注业务价值

## 2. 领域建模设计（单体版）

### 2.1 核心限界上下文（6个）

基于单体架构的实际需求，精简为6个核心限界上下文：

#### 2.1.1 用户身份上下文 (User Identity Context)
**包路径**：`com.procurement.user`

**聚合根**：
- `User` - 用户聚合根

**核心实体**：
- `UserProfile` - 用户档案
- `CompanyInfo` - 企业信息
- `CreditScore` - 信用评分

**值对象**：
- `UserId`, `Email`, `Phone`, `Address`
- `UserRole`, `UserStatus`, `CreditLevel`

**领域服务**：
- `UserRegistrationService` - 用户注册服务
- `CreditEvaluationService` - 信用评估服务

#### 2.1.2 采购需求上下文 (Procurement Requirement Context)
**包路径**：`com.procurement.requirement`

**聚合根**：
- `ProcurementRequirement` - 采购需求聚合根

**核心实体**：
- `RequirementItem` - 需求项目
- `TechnicalSpecification` - 技术规格
- `BiddingRule` - 竞价规则

**值对象**：
- `RequirementId`, `ProductCategory`, `Quantity`
- `DeliveryRequirement`, `QualityStandard`

**领域服务**：
- `RequirementValidationService` - 需求验证服务
- `RequirementMatchingService` - 需求匹配服务

#### 2.1.3 竞价流程上下文 (Bidding Process Context)
**包路径**：`com.procurement.bidding`

**聚合根**：
- `BiddingProcess` - 竞价流程聚合根

**核心实体**：
- `Bid` - 投标
- `BidEvaluation` - 投标评估
- `SupplierProposal` - 供应商方案

**值对象**：
- `BiddingId`, `BidPrice`, `DeliveryTime`
- `EvaluationCriteria`, `BiddingStatus`

**领域服务**：
- `BidEvaluationService` - 投标评估服务
- `SupplierSelectionService` - 供应商选择服务

#### 2.1.4 订单履约上下文 (Order Fulfillment Context)
**包路径**：`com.procurement.order`

**聚合根**：
- `Order` - 订单聚合根

**核心实体**：
- `OrderItem` - 订单项目
- `DeliverySchedule` - 交付计划
- `QualityInspection` - 质量检验

**值对象**：
- `OrderId`, `OrderStatus`, `PaymentTerms`
- `DeliveryAddress`, `InspectionResult`

**领域服务**：
- `OrderFulfillmentService` - 订单履约服务
- `QualityControlService` - 质量控制服务

#### 2.1.5 物流服务上下文 (Logistics Service Context)
**包路径**：`com.procurement.logistics`

**聚合根**：
- `Shipment` - 物流聚合根

**核心实体**：
- `LogisticsProvider` - 物流服务商
- `TrackingRecord` - 跟踪记录
- `DeliveryConfirmation` - 交付确认

**值对象**：
- `ShipmentId`, `TrackingNumber`, `LogisticsStatus`
- `ShippingAddress`, `DeliveryTime`

**领域服务**：
- `LogisticsCoordinationService` - 物流协调服务
- `TrackingService` - 跟踪服务

#### 2.1.6 财务结算上下文 (Financial Settlement Context)
**包路径**：`com.procurement.finance`

**聚合根**：
- `Settlement` - 结算聚合根

**核心实体**：
- `PaymentTransaction` - 支付交易
- `CommissionCalculation` - 佣金计算
- `FinancialAccount` - 财务账户

**值对象**：
- `SettlementId`, `Amount`, `Currency`
- `PaymentMethod`, `SettlementStatus`

**领域服务**：
- `SettlementCalculationService` - 结算计算服务
- `PaymentProcessingService` - 支付处理服务

### 2.2 上下文集成设计（单体版）

#### 2.2.1 模块间通信
使用Spring Events实现模块间的松耦合通信：

```kotlin
// 领域事件定义
sealed interface DomainEvent {
    val eventId: String
    val occurredOn: Instant
    val aggregateId: String
}

// 用户注册事件
data class UserRegisteredEvent(
    override val eventId: String = UUID.randomUUID().toString(),
    override val occurredOn: Instant = Instant.now(),
    override val aggregateId: String,
    val userId: UserId,
    val userRole: UserRole
) : DomainEvent

// 事件监听器
@Component
class UserEventHandler {
    
    @EventListener
    @Async
    fun handle(event: UserRegisteredEvent) {
        // 初始化用户信用档案
        creditService.initializeCreditProfile(event.userId)
    }
}
```

#### 2.2.2 数据一致性
在单体架构中，使用数据库事务保证一致性：

```kotlin
@Service
@Transactional
class OrderCreationService {
    
    fun createOrder(command: CreateOrderCommand): OrderId {
        // 1. 验证采购需求
        val requirement = requirementRepository.findById(command.requirementId)
            ?: throw RequirementNotFoundException()
        
        // 2. 创建订单
        val order = Order.create(command)
        orderRepository.save(order)
        
        // 3. 发布事件（事务提交后执行）
        eventPublisher.publishEvent(OrderCreatedEvent(order.id))
        
        return order.id
    }
}
```

## 3. 技术实现架构

### 3.1 包结构设计

```
src/main/kotlin/com/procurement/
├── shared/                          # 共享内核
│   ├── domain/                      # 领域基础设施
│   │   ├── AggregateRoot.kt
│   │   ├── Entity.kt
│   │   ├── ValueObject.kt
│   │   └── DomainEvent.kt
│   ├── infrastructure/              # 基础设施
│   │   ├── persistence/
│   │   ├── messaging/
│   │   └── web/
│   └── application/                 # 应用服务基础
├── user/                           # 用户身份上下文
│   ├── domain/
│   │   ├── model/
│   │   ├── repository/
│   │   └── service/
│   ├── application/
│   │   ├── service/
│   │   └── dto/
│   └── infrastructure/
│       ├── persistence/
│       └── web/
├── requirement/                    # 采购需求上下文
├── bidding/                       # 竞价流程上下文
├── order/                         # 订单履约上下文
├── logistics/                     # 物流服务上下文
└── finance/                       # 财务结算上下文
```

### 3.2 聚合根设计模式

```kotlin
@Entity
@Table(name = "users")
data class User(
    @Id
    @Column(name = "id")
    val id: UserId,
    
    @Embedded
    var profile: UserProfile,
    
    @Enumerated(EnumType.STRING)
    var status: UserStatus,
    
    @Enumerated(EnumType.STRING)
    val role: UserRole,
    
    @Version
    var version: Long = 0
    
) : AggregateRoot<UserId>() {
    
    // 业务方法
    fun activate(): User = apply {
        require(status == UserStatus.INACTIVE) { "User is not inactive" }
        status = UserStatus.ACTIVE
        addDomainEvent(UserActivatedEvent(id))
    }
    
    fun updateProfile(newProfile: UserProfile): User = apply {
        val oldProfile = profile
        profile = newProfile
        addDomainEvent(UserProfileUpdatedEvent(id, oldProfile, newProfile))
    }
}
```

### 3.3 应用服务设计

```kotlin
@Service
@Transactional
class UserApplicationService(
    private val userRepository: UserRepository,
    private val eventPublisher: ApplicationEventPublisher
) {
    
    suspend fun registerUser(command: RegisterUserCommand): UserId {
        // 1. 验证业务规则
        validateUserRegistration(command)
        
        // 2. 创建用户聚合
        val user = User.register(command)
        
        // 3. 持久化
        userRepository.save(user)
        
        // 4. 发布领域事件
        user.domainEvents.forEach { event ->
            eventPublisher.publishEvent(event)
        }
        
        return user.id
    }
}
```

## 4. 数据模型设计

### 4.1 核心聚合表结构

```sql
-- 用户聚合
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    role VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    profile JSONB NOT NULL,
    credit_score INTEGER DEFAULT 600,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0
);

-- 采购需求聚合
CREATE TABLE procurement_requirements (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    buyer_id VARCHAR(36) NOT NULL REFERENCES users(id),
    requirements JSONB NOT NULL,
    bidding_rules JSONB NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0
);
```

### 4.2 事件存储设计

```sql
-- 领域事件存储
CREATE TABLE domain_events (
    id VARCHAR(36) PRIMARY KEY,
    event_type VARCHAR(255) NOT NULL,
    aggregate_id VARCHAR(36) NOT NULL,
    aggregate_type VARCHAR(255) NOT NULL,
    event_data JSONB NOT NULL,
    occurred_on TIMESTAMP NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_domain_events_aggregate ON domain_events(aggregate_id, aggregate_type);
CREATE INDEX idx_domain_events_type ON domain_events(event_type);
CREATE INDEX idx_domain_events_occurred ON domain_events(occurred_on);
```

## 5. 总结

### 5.1 单体DDD架构优势

1. **清晰的领域边界**：6个核心上下文覆盖完整业务流程
2. **模块化设计**：包结构清晰，职责分离明确
3. **事件驱动解耦**：Spring Events实现模块间松耦合
4. **数据一致性**：数据库事务保证强一致性
5. **演进友好**：为未来微服务化预留清晰边界

### 5.2 技术实现价值

- **开发效率**：Kotlin + Spring Boot的现代化开发体验
- **代码质量**：DDD模式确保业务逻辑清晰
- **维护成本**：单体架构降低运维复杂度
- **扩展能力**：模块化设计支持功能扩展

这个设计专门针对单体架构优化，在保持DDD核心价值的同时，避免了分布式系统的复杂性。
